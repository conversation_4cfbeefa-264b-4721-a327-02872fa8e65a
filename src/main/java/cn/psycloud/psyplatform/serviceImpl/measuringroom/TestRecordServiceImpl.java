package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.util.Base64Util;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.deepoove.poi.data.PictureRenderData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.ageOfNow;

@Slf4j
@Service
public class TestRecordServiceImpl implements TestRecordService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ScaleDao scaleDao;
    @Autowired
    private ScaleFactorDao scaleFactorDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private TestScoreService testScoreService;
    @Autowired
    private POIWordHelper poiWordHelper;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  根据记录ID查询测评记录
     * @param recordId 记录id
     * @return 测评记录实体对象
     */
    @Override
    public TestRecordDto getById(Integer recordId) {
        var testRecord = testRecordDao.getById(recordId);
        var scale = scaleDao.getById(testRecord.getScaleId());
        List<ScaleQuestionEntity> listQuestions = scaleQuestionDao.getListByScaleIdForTestIng(scale.getId());
        scale.setListQuestions(listQuestions);
        var userDto = new UserDto();
        userDto.setUserId(testRecord.getUser().getUserId());
        var user = userDao.get(userDto);
        testRecord.setScale(scale);
        testRecord.setUser(user);
        return  testRecord;
    }

    /**
     *  查询测评记录集合：分页
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public BSDatatableRes<TestRecordDto> getListByPaged(TestRecordDto dto) {
        var dtRes = new BSDatatableRes<TestRecordDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getList(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询测评记录集合
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public List<TestRecordDto> getList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getList(dto);
    }

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    public BSDatatableRes<TestRecordDto> getMyRecords(TestRecordDto dto){
        var dtRes = new BSDatatableRes<TestRecordDto>();
        // 修复分页计算：pageIndex是偏移量，需要转换为页码
        int pageNum = dto.getPageIndex() / dto.getPageSize() + 1;
        PageHelper.startPage(pageNum, dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getMyRecords(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     *  保存测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    @Override
    public int addRecord(TestRecordEntity entity) {
        entity.setId(0);
        testRecordDao.addTestRecord(entity);
        return  entity.getId();
    }

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return testRecordDao.deleteByRecordId(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  更新测评状态
     * @param recordId 记录id
     * @param state 测评状态：0-未完成 1-已完成 2-测谎未通过
     * @return 影响行数
     */
    @Override
    public int updateTestState(Integer recordId, Integer state) {
        var map = new HashMap<String, Integer>();
        map.put("recordId",recordId);
        map.put("state",state);
        return testRecordDao.updateTestState(map);
    }

    /**
     *  更新测评开始时间
     * @param recordId 记录id
     * @return 影响行数
     */
    @Override
    public int updateTestStartTime(Integer recordId) {
        var map = new HashMap<String, Object>();
        map.put("recordId",recordId);
        map.put("startTime",new Date());
        return testRecordDao.updateStartTime(map);
    }

    /**
     *  验证个人信息是否符合量表要求
     * @param dto 测评记录实体对象
     * @return 是否符合
     */
    @Override
    public boolean isUserInfoComplete(TestRecordDto dto) {
        boolean check = false;
        var limit = dto.getScale().getTestLimit();
        if (limit == null || "".equals(limit) ) {
            check = true;
        }
        else {
            var userDto = new UserDto();
            userDto.setUserId(dto.getUser().getUserId());
            var user = userDao.get(userDto);
            if (limit.contains("出生年月")) {
                if (user.getBirth() != null && user.getBirth() != "") {
                    String[] arrayAgeLimit = dto.getScale().getAgeLimit().split(",");
                    if ("0".equals(arrayAgeLimit[0]) && "0".equals(arrayAgeLimit[1]))
                        check = true;
                    else {
                        var age = ageOfNow(user.getBirth());
                        if (age >= Integer.parseInt(arrayAgeLimit[0]) && age <= Integer.parseInt(arrayAgeLimit[1])) {
                            check = true;
                        }
                    }
                }
            }
            if (limit.contains("性别")) check = user.getSex() != null && !"".equals(user.getSex());
            if (limit.contains("姓名")) check = user.getRealName() != null && !"".equals(user.getRealName());
        }
        return check;
    }

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 异常标识
     */
    @Override
    public int isAbnormal(Integer recordId) {
        return testRecordDao.isAbnormal(recordId);
    }

    /**
     *  查询九型人格测评记录集合：分页
     * @param dto 九型人格测评实体对象
     * @return 记录集合
     */
    @Override
    public BSDatatableRes<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto) {
        var dtRes = new BSDatatableRes<NineHouseStatDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<NineHouseStatDto> listRecords = testRecordDao.getNineHouseList(dto);
        PageInfo<NineHouseStatDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return  dtRes;
    }

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordExplain(TestRecordExplainEntity entity){
        return testRecordDao.saveTestRecordExplain(entity) > 0 ;
    }

    /**
     * 获取因子结果解释
     * @param recordId 测评记录id
     * @return 因子结果解释列表
     */
    @Override
    public List<TestRecordExplainEntity> getFactorExplains(Integer recordId) {
        return testRecordDao.getFactorExplains(recordId);
    }

    /**
     *  获取报告图表
     * @param recordId 测评记录id
     * @return 测评报告图表集合
     */
    public List<TestRecordChartsEntity> getReportCharts(Integer recordId) {
        return testRecordDao.getReportCharts(recordId);
    }

    /**
     *  保存测评报告里的图表
     * @param dto 测评报告图表实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordCharts(TestRecordChartsDto dto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(dto.getRecordId());
        if(dto.getChartsImg() != null) {
            int chartOrder = 0;
            for(String chartImg: dto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                Base64Util.generateImage(chartImg.replace("data:image/png;base64,",""),uploadPath+"/charts/"+fileName);
                var chartsEntity = new TestRecordChartsEntity();
                chartsEntity.setRecordId(dto.getRecordId());
                chartsEntity.setChartsImg(fileName);
                chartsEntity.setChartOrder(chartOrder++);
                chartsEntity.setFactorType(dto.getFactorType());
                chartsEntity.setChartType(dto.getChartType());
                chartsEntity.setChartIndex(dto.getChartIndex());
                isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  保存测评报告里的图表（新版本，支持详细图表信息）
     * @param requestDto 测评报告图表请求实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordChartsV2(TestRecordChartsRequestDto requestDto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(requestDto.getRecordId());
        if(requestDto.getChartsImg() != null && !requestDto.getChartsImg().isEmpty()) {
            int chartOrder = 0;
            for(ChartInfoDto chartInfo: requestDto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                String imageData = chartInfo.getImageData();
                if(imageData != null) {
                    imageData = imageData.replace("data:image/png;base64,","");
                    Base64Util.generateImage(imageData, uploadPath+"/charts/"+fileName);

                    var chartsEntity = new TestRecordChartsEntity();
                    chartsEntity.setRecordId(requestDto.getRecordId());
                    chartsEntity.setChartsImg(fileName);
                    chartsEntity.setChartOrder(chartOrder++);
                    chartsEntity.setFactorType(chartInfo.getFactorType());
                    chartsEntity.setChartType(chartInfo.getChartType());
                    chartsEntity.setChartIndex(chartInfo.getChartIndex());
                    isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
                }
            }
        }
        return isSuccess;
    }

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    @Override
    public List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getExportTestRecordList(dto);
    }

    /**
     *  测评报告导出word
     * @param recordId 测评记录Id
     * @return 文件名
     */
    @Override
    public String exportTestReportToWord(Integer recordId, String folderName){
        String filePath = "";
        try {
            var reportDto = getReport(recordId);
            if(reportDto.getListExplains() != null && reportDto.getListExplains().size() > 0) {
                Map<String, Object> reportMap = new HashMap<>();

                // 基本信息
                TestRecordDto testRecord = reportDto.getTestRecord();
                UserDto user = testRecord.getUser();
                ScaleDto scale = testRecord.getScale();
                
                // 格式化基本信息
                String userName = (user.getRealName() != null && !user.getRealName().isEmpty()) ? 
                    user.getRealName() : user.getLoginName();
                String startDate = new SimpleDateFormat("yyyy-MM-dd").format(testRecord.getStartTime());
                String costTime = formatSeconds(testRecord.getTimeInterval());
                
                // 构建基本信息HTML
                StringBuilder basicInfoHtml = new StringBuilder();
                basicInfoHtml.append("<div style='margin-bottom: 20px;'>");
                basicInfoHtml.append("<table style='width: 100%; border-collapse: collapse; border: 1px solid #e8ecf4;'>");
                basicInfoHtml.append("<tr style='background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);'>");
                basicInfoHtml.append("<th style='padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50; vertical-align: middle;'>姓名</th>");
                basicInfoHtml.append("<th style='padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50; vertical-align: middle;'>所属组织</th>");
                basicInfoHtml.append("<th style='padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50; vertical-align: middle;'>测试日期</th>");
                basicInfoHtml.append("<th style='padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50; vertical-align: middle;'>耗时</th>");
                basicInfoHtml.append("</tr>");
                basicInfoHtml.append("<tr>");
                basicInfoHtml.append("<td style='padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>").append(escapeHtml(userName)).append("</td>");
                basicInfoHtml.append("<td style='padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>").append(escapeHtml(user.getStructName())).append("</td>");
                basicInfoHtml.append("<td style='padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>").append(escapeHtml(startDate)).append("</td>");
                basicInfoHtml.append("<td style='padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>").append(escapeHtml(costTime)).append("</td>");
                basicInfoHtml.append("</tr>");
                basicInfoHtml.append("</table>");
                basicInfoHtml.append("</div>");

                // 构建因子结果分析HTML
                StringBuilder factorAnalysisHtml = new StringBuilder();
                factorAnalysisHtml.append("<div style='margin-top: 30px;'>");
                factorAnalysisHtml.append("<h3 style='color: #2c3e50; border-bottom: 2px solid #727cf5; padding-bottom: 10px; margin-bottom: 20px;'>");
                factorAnalysisHtml.append("结果分析");
                factorAnalysisHtml.append("</h3>");
                
                // 处理因子层级数据 - 按照前端逻辑处理
                List<FactorExplainHierarchyDto> factorList = reportDto.getListExplains();
                if (factorList != null && !factorList.isEmpty()) {
                    // 按照前端逻辑组织因子数据
                    List<FactorExplainHierarchyDto> topLevelFactors = new ArrayList<>();
                    List<FactorExplainHierarchyDto> independentFactors = new ArrayList<>();
                    List<ParentGroup> parentGroups = new ArrayList<>();
                    Set<Integer> processedFactorIds = new HashSet<>();
                    
                    // 先处理顶层因子和直接子因子
                    for (FactorExplainHierarchyDto factorData : factorList) {
                        if (factorData.getChildren() != null && !factorData.getChildren().isEmpty()) {
                            // 顶层因子（有子因子的因子）
                            if (!topLevelFactors.stream().anyMatch(f -> f.getFactorId().equals(factorData.getFactorId()))) {
                                topLevelFactors.add(factorData);
                            }
                            // 创建父组
                            if (!parentGroups.stream().anyMatch(g -> g.getParentId().equals(factorData.getFactorId()))) {
                                parentGroups.add(new ParentGroup(factorData.getFactorId(), factorData.getFactorName(), factorData.getChildren()));
                            }
                        } else if (!processedFactorIds.contains(factorData.getFactorId())) {
                            // 独立因子（没有父因子的因子）
                            if (!independentFactors.stream().anyMatch(f -> f.getFactorId().equals(factorData.getFactorId()))) {
                                independentFactors.add(factorData);
                            }
                            processedFactorIds.add(factorData.getFactorId());
                        }
                    }
                    
                    // 然后递归收集因子
                    collectFactors(factorList, null, topLevelFactors, parentGroups, independentFactors, processedFactorIds);
                    
                    // 生成顶层因子内容
                    if (!topLevelFactors.isEmpty()) {
                        factorAnalysisHtml.append(generateFactorContent(topLevelFactors, "结果解释及建议", "topLevel", recordId));
                    }
                    
                    // 生成父因子组内容
                    for (ParentGroup parentGroup : parentGroups) {
                        factorAnalysisHtml.append(generateFactorContent(parentGroup.children, parentGroup.parentName, "parent_" + parentGroup.parentId, recordId));
                    }
                    
                    // 生成独立因子内容
                    if (!independentFactors.isEmpty()) {
                        factorAnalysisHtml.append(generateFactorContent(independentFactors, "结果解释及建议", "independent", recordId));
                    }
                } else {
                    factorAnalysisHtml.append("<div style='padding: 20px; background-color: #f8f9ff; border: 1px solid #e8ecf4; border-radius: 5px;'>");
                    factorAnalysisHtml.append("<p style='margin: 0; color: #6c757d;'><i class='fa fa-info-circle mr-2'></i>暂无数据</p>");
                    factorAnalysisHtml.append("</div>");
                }
                
                factorAnalysisHtml.append("</div>");

                // 构建完整的报告内容
                StringBuilder fullReportHtml = new StringBuilder();
                fullReportHtml.append("<div style='font-family: \"宋体\", Arial, sans-serif; line-height: 1.6; color: #2c3e50;'>");
                fullReportHtml.append("<div style='text-align: center; margin-bottom: 30px;'>");
                fullReportHtml.append("<h1 style='color: #2c3e50; margin-bottom: 10px; text-align: center;'>《").append(scale.getScaleName()).append("》<br/>测评报告</h1>");
                fullReportHtml.append("</div>");
                
                // 基本信息
                fullReportHtml.append("<div style='margin-bottom: 30px;'>");
                fullReportHtml.append("<h2 style='color: #2c3e50; border-bottom: 2px solid #727cf5; padding-bottom: 10px; margin-bottom: 20px;'>基本信息</h2>");
                fullReportHtml.append(basicInfoHtml);
                fullReportHtml.append("</div>");
                
                // 因子结果分析
                fullReportHtml.append(factorAnalysisHtml);
                
                // 图表分析已集成到因子分析中
                
                fullReportHtml.append("</div>");

                // 设置报告数据
                reportMap.put("basicInfo", basicInfoHtml.toString());
                reportMap.put("factorAnalysis", factorAnalysisHtml.toString());
                reportMap.put("chartsAnalysis", ""); // 图表已集成到因子分析中
                reportMap.put("fullReport", fullReportHtml.toString());
                reportMap.put("userName", userName);
                reportMap.put("structName", user.getStructName());
                reportMap.put("startDate", startDate);
                reportMap.put("costTime", costTime);
                reportMap.put("scaleName", scale.getScaleName());
                
                // 图表数据已直接嵌入到HTML中，无需单独添加

                // 使用现有模板文件
                String templatePath = CommonHelper.getResourcesFilePath("static/template/report_new.docx");
                String fileName = reportDto.getTestRecord().getId() + "_" + userName + "_" + scale.getScaleName() + ".docx";
                filePath = String.format("report/%s/%s", folderName, fileName);
                String fileAbPath = String.format(uploadPath + "report/%s/%s", folderName, fileName);

                // 使用HTML渲染配置生成Word文档
                try {
                    File newFile = new File(fileAbPath);
                    if(!newFile.getParentFile().exists()){
                        newFile.getParentFile().mkdirs();
                    }

                    // 创建HTML渲染策略，配置支持更多HTML样式
                    org.ddr.poi.html.HtmlRenderPolicy htmlRenderPolicy = new org.ddr.poi.html.HtmlRenderPolicy();

                    // 配置HTML渲染选项（如果poi-tl-ext版本支持）
                    try {
                        // 尝试设置HTML渲染配置，增强样式支持
                        Class<?> configClass = Class.forName("org.ddr.poi.html.HtmlRenderConfig");
                        Object config = configClass.newInstance();

                        // 设置全局字体（如果支持）
                        try {
                            configClass.getMethod("setGlobalFont", String.class).invoke(config, "宋体");
                        } catch (Exception ignored) {}

                        // 设置全局字号（如果支持）
                        try {
                            configClass.getMethod("setGlobalFontSize", Integer.class).invoke(config, 11);
                        } catch (Exception ignored) {}

                        // 应用配置到渲染策略（如果支持）
                        try {
                            htmlRenderPolicy.getClass().getMethod("setConfig", configClass).invoke(htmlRenderPolicy, config);
                        } catch (Exception ignored) {}

                    } catch (Exception e) {
                        // 如果配置失败，使用默认配置
                        log.debug("HTML渲染配置设置失败，使用默认配置: {}", e.getMessage());
                    }

                    com.deepoove.poi.config.Configure config = com.deepoove.poi.config.Configure.builder()
                            .bind("fullReport", htmlRenderPolicy)
                            .bind("basicInfo", htmlRenderPolicy)
                            .bind("factorAnalysis", htmlRenderPolicy)
                            .bind("chartsAnalysis", htmlRenderPolicy)
                            .useSpringEL() // 启用Spring表达式语言支持
                            .build();

                    com.deepoove.poi.XWPFTemplate template = com.deepoove.poi.XWPFTemplate.compile(templatePath, config).render(reportMap);

                    try (java.io.FileOutputStream out = new java.io.FileOutputStream(fileAbPath)) {
                        template.write(out);
                    }
                    template.close();
                } catch (Exception e) {
                    log.error("生成Word文档失败: {}", e.getMessage(), e);
                    throw new RuntimeException("生成Word文档失败", e);
                }
            }
        } catch (Exception e) {
            log.error("导出测评报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出测评报告失败", e);
        }
        return filePath;
    }

    /**
     *  批量导出测评报告
     * @param dto 查询条件
     * @return 文件路径
     */
    @Override
    public String batchExportReport(TestRecordDto dto) {
        String result = "";
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        var recordIds = testRecordDao.getRecordIdsForBatchExport(dto);
        if(!recordIds.isEmpty()) {
            result = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            for(Map<String,Integer> map: recordIds){
                exportTestReportToWord(map.get("id"), result);
            }
        }
        return  result;
    }

    /**
     * 获取因子结果解释（按父子关系组织）
     * @param recordId 测评记录id
     * @return 按父子关系组织的因子解释
     */
    @Override
    public List<FactorExplainHierarchyDto> getFactorExplainsWithHierarchy(Integer recordId) {
        // 获取因子解释
        List<TestRecordExplainEntity> explains = testRecordDao.getFactorExplains(recordId);

        // 获取测评记录以获取量表ID
        TestRecordDto testRecord = getById(recordId);
        Integer scaleId = testRecord.getScale().getId();

        // 获取该量表的所有因子信息（包含factorIds）
        List<ScaleFactorDto> factors = scaleFactorDao.getFactorsByScaleId(scaleId);

        // 创建因子ID到解释的映射
        Map<Integer, TestRecordExplainEntity> explainMap = explains.stream()
                .collect(Collectors.toMap(TestRecordExplainEntity::getFactorId, e -> e));

        // 创建因子ID到因子信息的映射
        Map<Integer, ScaleFactorDto> factorMap = factors.stream()
                .collect(Collectors.toMap(ScaleFactorDto::getId, f -> f));

        // 找出所有父因子（有factorIds的因子）
        List<ScaleFactorDto> parentFactors = factors.stream()
                .filter(f -> f.getFactorIds() != null && !f.getFactorIds().trim().isEmpty())
                .sorted(Comparator.comparing(ScaleFactorDto::getFactorType).reversed()) // 按类型倒序，高级因子在前
                .collect(Collectors.toList());

        // 构建层级结构
        List<FactorExplainHierarchyDto> result = new ArrayList<>();
        Set<Integer> usedFactorIds = new HashSet<>();

        for (ScaleFactorDto parentFactor : parentFactors) {
            if (explainMap.containsKey(parentFactor.getId()) && !usedFactorIds.contains(parentFactor.getId())) {
                FactorExplainHierarchyDto parentNode = buildFactorHierarchy(
                        parentFactor, explainMap, factorMap, usedFactorIds);
                if (parentNode != null) {
                    result.add(parentNode);
                }
            }
        }

        // 添加没有父因子的独立因子
        for (TestRecordExplainEntity explain : explains) {
            if (!usedFactorIds.contains(explain.getFactorId())) {
                FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();
                node.setFactorId(explain.getFactorId());
                node.setFactorName(explain.getFactorName());
                node.setInterpretation(explain.getInterpretation());
                node.setOriginalScore(explain.getOriginalScore());
                node.setScore(explain.getScore());
                node.setChildren(new ArrayList<>());
                result.add(node);
            }
        }

        return result;
    }

    private FactorExplainHierarchyDto buildFactorHierarchy(
            ScaleFactorDto factor,
            Map<Integer, TestRecordExplainEntity> explainMap,
            Map<Integer, ScaleFactorDto> factorMap,
            Set<Integer> usedFactorIds) {

        if (!explainMap.containsKey(factor.getId())) {
            return null;
        }

        TestRecordExplainEntity explain = explainMap.get(factor.getId());
        FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();

        // 设置因子基本信息
        node.setFactorId(explain.getFactorId());
        node.setFactorName(explain.getFactorName());
        node.setInterpretation(explain.getInterpretation());
        node.setFactorType(factor.getFactorType());
        node.setChildren(new ArrayList<>());
        node.setOriginalScore(explain.getOriginalScore());
        node.setScore(explain.getScore());

        usedFactorIds.add(factor.getId());

        // 处理子因子
        if (factor.getFactorIds() != null && !factor.getFactorIds().trim().isEmpty()) {
            String[] childIds = factor.getFactorIds().split(",");
            for (String childIdStr : childIds) {
                try {
                    Integer childId = Integer.parseInt(childIdStr.trim());
                    if (factorMap.containsKey(childId) && explainMap.containsKey(childId)) {
                        ScaleFactorDto childFactor = factorMap.get(childId);

                        // 递归构建子因子
                        FactorExplainHierarchyDto childNode = buildFactorHierarchy(
                                childFactor, explainMap, factorMap, usedFactorIds);
                        if (childNode != null) {
                            node.getChildren().add(childNode);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }
        return node;
    }

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评报告实体对象
     */
    @Override
    public ReportDto getReport(Integer recordId) {
        var reportDto = new ReportDto();
        reportDto.setTestRecord(getById(recordId));
        reportDto.setListExplains(getFactorExplainsWithHierarchy(recordId));
        reportDto.setListCharts(getReportCharts(recordId));
        return reportDto;
    }



    /**
     * 格式化秒数为可读时间
     * @param seconds 秒数
     * @return 格式化后的时间字符串
     */
    private String formatSeconds(Integer seconds) {
        if (seconds == null || seconds <= 0) {
            return "0分钟";
        }
        
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int secs = seconds % 60;
        
        StringBuilder result = new StringBuilder();
        if (hours > 0) {
            result.append(hours).append("小时");
        }
        if (minutes > 0) {
            result.append(minutes).append("分钟");
        }
        if (secs > 0 && hours == 0) {
            result.append(secs).append("秒");
        }
        
        return result.toString();
    }

    /**
     * HTML转义方法
     * @param text 需要转义的文本
     * @return 转义后的文本
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }

    /**
     * 处理HTML内容以便在Word中正确显示样式
     * @param text 包含HTML标签的文本
     * @return 处理后的HTML文本，保留poi-tl-ext支持的标签和样式
     */
    private String convertHtmlToDisplayStyle(String text) {
        if (text == null) {
            return "";
        }

        // 保留HTML标签，让poi-tl-ext进行渲染
        // poi-tl-ext支持的HTML标签：p, br, strong, b, em, i, u, s, ul, ol, li, h1-h6等
        String result = text
                // 规范化段落标签
                .replaceAll("<p\\s+[^>]*>", "<p>")  // 移除p标签的属性，保留标签本身

                // 规范化换行标签
                .replaceAll("<br[^>]*/?\\s*>", "<br/>")

                // 规范化加粗标签 - 统一使用strong
                .replaceAll("<b\\s*[^>]*>", "<strong>")
                .replaceAll("</b>", "</strong>")

                // 规范化斜体标签 - 统一使用em
                .replaceAll("<i\\s*[^>]*>", "<em>")
                .replaceAll("</i>", "</em>")

                // 保留下划线标签
                .replaceAll("<u\\s*[^>]*>", "<u>")

                // 保留删除线标签
                .replaceAll("<s\\s*[^>]*>", "<s>")
                .replaceAll("<strike\\s*[^>]*>", "<s>")
                .replaceAll("</strike>", "</s>")
                .replaceAll("<del\\s*[^>]*>", "<s>")
                .replaceAll("</del>", "</s>")

                // 规范化标题标签
                .replaceAll("<h([1-6])\\s*[^>]*>", "<h$1>")

                // 规范化列表标签
                .replaceAll("<ul\\s*[^>]*>", "<ul>")
                .replaceAll("<ol\\s*[^>]*>", "<ol>")
                .replaceAll("<li\\s*[^>]*>", "<li>")

                // 处理span标签 - 如果有style属性则保留，否则移除
                .replaceAll("<span\\s*>", "")  // 移除没有属性的span
                .replaceAll("</span>", "")

                // 处理div标签 - 转换为段落
                .replaceAll("<div[^>]*>", "<p>")
                .replaceAll("</div>", "</p>")

                // 处理font标签 - 转换为span with style
                .replaceAll("<font\\s+color\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>", "<span style='color: $1;'>")
                .replaceAll("<font\\s+size\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>", "<span style='font-size: $1;'>")
                .replaceAll("</font>", "</span>")

                // 清理多余的空白段落和换行
                .replaceAll("(<p>\\s*</p>)", "")
                .replaceAll("(<br/>\\s*){3,}", "<br/><br/>")
                .replaceAll("^(<br/>\\s*)+", "")
                .replaceAll("(<br/>\\s*)+$", "");

        return result;
    }

    /**
     * 父因子组类
     */
    private static class ParentGroup {
        private Integer parentId;
        private String parentName;
        private List<FactorExplainHierarchyDto> children;

        public ParentGroup(Integer parentId, String parentName, List<FactorExplainHierarchyDto> children) {
            this.parentId = parentId;
            this.parentName = parentName;
            this.children = children;
        }

        public Integer getParentId() { return parentId; }
        public String getParentName() { return parentName; }
        public List<FactorExplainHierarchyDto> getChildren() { return children; }
    }

    /**
     * 父因子信息类
     */
    private static class ParentInfo {
        private Integer parentId;
        private String parentName;

        public ParentInfo(Integer parentId, String parentName) {
            this.parentId = parentId;
            this.parentName = parentName;
        }

        public Integer getParentId() { return parentId; }
        public String getParentName() { return parentName; }
    }

    /**
     * 生成因子内容（包含表格和图表）- 使用百分比宽度表格
     */
    private String generateFactorContent(List<FactorExplainHierarchyDto> factors, String title, String chartPrefix, Integer recordId) {
        if (factors == null || factors.isEmpty()) {
            return "";
        }

        StringBuilder content = new StringBuilder();
        content.append("<div style='margin-bottom: 30px;'>");
        content.append("<h4 style='color: #727cf5; margin-bottom: 15px;'>");
        if (title.equals("结果解释及建议")) {
            content.append("<i class='fa fa-lightbulb-o mr-2'></i>");
        } else {
            content.append("<i class='fa fa-folder-open mr-2'></i>");
        }
        content.append(escapeHtml(title)).append("</h4>");

        // 使用百分比宽度的HTML表格
        content.append(generateFactorTableHtml(factors));

        // 图表分析部分
        content.append("<div style='margin-top: 20px;'>");
        content.append("<h5 style='color: #727cf5; margin-bottom: 15px;'>图表分析</h5>");
        content.append(generateChartContainers(factors, chartPrefix, recordId));
        content.append("</div>");

        content.append("</div>");
        return content.toString();
    }

    /**
     * 生成因子表格HTML - 使用百分比宽度（第一列20%，第二列80%）
     */
    private String generateFactorTableHtml(List<FactorExplainHierarchyDto> factors) {
        StringBuilder tableHtml = new StringBuilder();

        tableHtml.append("<table style='width: 100%; border-collapse: collapse; border: 1px solid #e8ecf4;'>");

        // 表头
        tableHtml.append("<tr style='background: #f8f9ff;'>");
        tableHtml.append("<th style='width: 20%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>因子名称</th>");
        tableHtml.append("<th style='width: 80%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>结果解释</th>");
        tableHtml.append("</tr>");

        // 表体
        for (FactorExplainHierarchyDto factor : factors) {
            tableHtml.append("<tr>");
            tableHtml.append("<td style='width: 20%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                    .append(escapeHtml(factor.getFactorName())).append("</td>");
            tableHtml.append("<td style='width: 80%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle; line-height: 1.6;'>")
                    .append(convertHtmlToDisplayStyle(factor.getInterpretation() != null ? factor.getInterpretation() : "暂无解释内容")).append("</td>");
            tableHtml.append("</tr>");
        }
        tableHtml.append("</table>");

        return tableHtml.toString();
    }

    /**
     * 生成图表容器 - 直接嵌入图片到HTML中
     */
    private String generateChartContainers(List<FactorExplainHierarchyDto> factors, String chartPrefix, Integer recordId) {
        StringBuilder content = new StringBuilder();
        
        // 查找对应的图表
        List<TestRecordChartsEntity> charts = getReportCharts(recordId);
        if (charts != null && !charts.isEmpty()) {
            // 根据chartPrefix和因子类型匹配对应的图表
            for (TestRecordChartsEntity chart : charts) {
                // 检查图表是否与当前因子组匹配
                boolean isMatched = false;
                
                // 根据chartPrefix判断匹配逻辑
                if (chartPrefix.equals("topLevel")) {
                    // 顶层因子图表
                    isMatched = chart.getFactorType() == null || "topLevel".equals(chart.getFactorType());
                } else if (chartPrefix.startsWith("parent_")) {
                    // 父因子组图表
                    //String parentIdStr = chartPrefix.replace("parent_", "");
                    isMatched = chartPrefix.equals(chart.getFactorType());
                } else if (chartPrefix.equals("independent")) {
                    // 独立因子图表
                    isMatched = chart.getFactorType() != null && !"independent".equals(chart.getFactorType());
                }
                
                if (isMatched) {
                    content.append("<div style='margin-bottom: 20px; text-align: center;'>");
                    // 然后尝试图片
                    String imagePath = uploadPath + "charts/" + chart.getChartsImg();
                    File imageFile = new File(imagePath);
                    
                    if (imageFile.exists()) {
                        try {
                            // 读取图片文件并转换为base64
                            byte[] imageBytes = java.nio.file.Files.readAllBytes(imageFile.toPath());
                            String base64Image = java.util.Base64.getEncoder().encodeToString(imageBytes);
                            String mimeType = "image/png"; // 假设是PNG格式
                            content.append("<img src='data:").append(mimeType).append(";base64,").append(base64Image).append("' style='max-width: 600px; max-height: 280px;' />");
                        } catch (Exception e) {
                            log.error("转换图片为base64失败: {}", e.getMessage(), e);
                            content.append("<p style='color: #6c757d;'>图片转换失败: ").append(chart.getChartsImg()).append("</p>");
                        }
                    } else {
                        content.append("<p style='color: #6c757d;'>图片文件不存在: ").append(chart.getChartsImg()).append("</p>");
                    }
                    content.append("</div>");
                }
            }
            
            // 如果没有找到匹配的图表，显示占位符
            if (content.length() == 0) {
                content.append("<div style='text-align: center; padding: 20px; background-color: #f8f9ff; border: 1px solid #e8ecf4; border-radius: 5px;'>");
                content.append("<p style='margin: 0; color: #6c757d;'>暂无图表数据</p>");
                content.append("</div>");
            }
        } else {
            // 如果没有图表，显示占位符
            content.append("<div style='text-align: center; padding: 20px; background-color: #f8f9ff; border: 1px solid #e8ecf4; border-radius: 5px;'>");
            content.append("<p style='margin: 0; color: #6c757d;'>暂无图表数据</p>");
            content.append("</div>");
        }
        
        return content.toString();
    }

    /**
     * 添加图表数据到reportMap - 根据因子类型匹配
     */
    private void addChartDataToReportMap(Map<String, Object> reportMap, Integer recordId) {
        List<TestRecordChartsEntity> charts = getReportCharts(recordId);
        if (charts != null && !charts.isEmpty()) {
            // 按照因子类型分组图表
            Map<String, List<TestRecordChartsEntity>> chartsByType = new HashMap<>();
            
            for (TestRecordChartsEntity chart : charts) {
                String factorType = chart.getFactorType();
                if (factorType == null || "0".equals(factorType)) {
                    factorType = "topLevel";
                } else {
                    factorType = "parent_" + factorType;
                }
                
                chartsByType.computeIfAbsent(factorType, k -> new ArrayList<>()).add(chart);
            }
            
            // 为每个因子类型添加图表数据
            int globalChartIndex = 0;
            for (Map.Entry<String, List<TestRecordChartsEntity>> entry : chartsByType.entrySet()) {
                for (TestRecordChartsEntity chart : entry.getValue()) {
                    try {
                        String imagePath = uploadPath + "/charts/" + chart.getChartsImg();
                        File imageFile = new File(imagePath);
                        
                        if (imageFile.exists()) {
                            String chartKey = "chartImg" + globalChartIndex;
                            PictureRenderData pictureData = new PictureRenderData(500, 330, imagePath);
                            reportMap.put(chartKey, pictureData);
                            globalChartIndex++;
                        }
                    } catch (Exception e) {
                        log.error("添加图表数据失败: {}", e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 递归收集因子的函数 - 按照前端逻辑实现
     */
    private void collectFactors(List<FactorExplainHierarchyDto> factors, 
                               ParentInfo parentInfo,
                               List<FactorExplainHierarchyDto> topLevelFactors,
                               List<ParentGroup> parentGroups,
                               List<FactorExplainHierarchyDto> independentFactors,
                               Set<Integer> processedFactorIds) {
        
        for (FactorExplainHierarchyDto factorData : factors) {
            if (processedFactorIds.contains(factorData.getFactorId())) continue;
            processedFactorIds.add(factorData.getFactorId());

            if (factorData.getChildren() != null && !factorData.getChildren().isEmpty()) {
                // 有子因子的情况，递归处理子因子
                collectFactors(factorData.getChildren(), 
                    new ParentInfo(factorData.getFactorId(), factorData.getFactorName()),
                    topLevelFactors, parentGroups, independentFactors, processedFactorIds);
            } else {
                // 没有子因子的情况
                if (parentInfo != null && factorData.getFactorType() != null && factorData.getFactorType() == 1) {
                    // 子因子，需要添加到父组
                    ParentGroup existingGroup = parentGroups.stream()
                        .filter(group -> group.getParentId().equals(parentInfo.parentId))
                        .findFirst()
                        .orElse(null);
                    
                    if (existingGroup != null) {
                        // 检查是否已存在
                        boolean exists = existingGroup.getChildren().stream()
                            .anyMatch(child -> child.getFactorId().equals(factorData.getFactorId()));
                        if (!exists) {
                            existingGroup.getChildren().add(factorData);
                        }
                    } else {
                        // 创建新的父组
                        List<FactorExplainHierarchyDto> children = new ArrayList<>();
                        children.add(factorData);
                        parentGroups.add(new ParentGroup(parentInfo.parentId, parentInfo.parentName, children));
                    }
                } else {
                    // 独立因子
                    boolean exists = independentFactors.stream()
                        .anyMatch(factor -> factor.getFactorId().equals(factorData.getFactorId()));
                    if (!exists) {
                        independentFactors.add(factorData);
                    }
                }
            }
        }
    }

    /**
     * 测试HTML样式在Word中的显示效果
     * @param recordId 测评记录ID
     * @return 测试结果
     */
    public String testHtmlStylesInWord(Integer recordId) {
        try {
            var reportDto = getReport(recordId);
            if (reportDto.getListExplains() == null || reportDto.getListExplains().isEmpty()) {
                return "测试失败：没有找到因子数据";
            }

            StringBuilder testResult = new StringBuilder();
            testResult.append("=== HTML样式在Word中的显示测试 ===\n\n");

            // 检查因子解释中的HTML内容
            List<FactorExplainHierarchyDto> factors = reportDto.getListExplains();
            int htmlCount = 0;
            int totalCount = 0;

            for (FactorExplainHierarchyDto factor : factors) {
                totalCount++;
                String interpretation = factor.getInterpretation();
                if (interpretation != null && interpretation.contains("<")) {
                    htmlCount++;
                    testResult.append("因子: ").append(factor.getFactorName()).append("\n");
                    testResult.append("原始HTML: ").append(interpretation.substring(0, Math.min(100, interpretation.length()))).append("...\n");

                    String processed = convertHtmlToDisplayStyle(interpretation);
                    testResult.append("处理后HTML: ").append(processed.substring(0, Math.min(100, processed.length()))).append("...\n\n");
                }
            }

            testResult.append("统计信息:\n");
            testResult.append("- 总因子数: ").append(totalCount).append("\n");
            testResult.append("- 包含HTML的因子数: ").append(htmlCount).append("\n\n");

            // 生成测试Word文档
            try {
                String testFileName = exportTestReportToWord(recordId, "html_style_test");
                testResult.append("测试Word文档生成成功: ").append(testFileName).append("\n");
                testResult.append("请检查Word文档中的HTML样式是否正确显示\n\n");
            } catch (Exception e) {
                testResult.append("Word文档生成失败: ").append(e.getMessage()).append("\n\n");
            }

            testResult.append("=== 支持的HTML标签 ===\n");
            testResult.append("- <p>段落</p>\n");
            testResult.append("- <br/>换行\n");
            testResult.append("- <strong>加粗</strong> 或 <b>加粗</b>\n");
            testResult.append("- <em>斜体</em> 或 <i>斜体</i>\n");
            testResult.append("- <u>下划线</u>\n");
            testResult.append("- <s>删除线</s>\n");
            testResult.append("- <h1>到<h6>标题</h6>\n");
            testResult.append("- <ul><li>无序列表</li></ul>\n");
            testResult.append("- <ol><li>有序列表</li></ol>\n");

            return testResult.toString();

        } catch (Exception e) {
            log.error("测试HTML样式失败: {}", e.getMessage(), e);
            return "测试失败：" + e.getMessage();
        }
    }


}
