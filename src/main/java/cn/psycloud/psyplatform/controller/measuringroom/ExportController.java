package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/measuringroom/export")
@Controller
public class ExportController {

    @Autowired
    private TestRecordService testRecordService;

    @GetMapping("/test_score")
    public String exportTestScore(){
        return "measuringroom/export/testScore";
    }

    /**
     * 测试表格列宽方案
     * 访问路径：/measuringroom/export/test-table-width?recordId=123
     */
    @GetMapping("/test-table-width")
    @ResponseBody
    public ResponseEntity<String> testTableWidth(@RequestParam Integer recordId) {
        try {
            String result = testRecordService.testTableColumnWidthSolutions(recordId);
            return ResponseEntity.ok()
                    .header("Content-Type", "text/plain; charset=utf-8")
                    .body(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body("测试失败：" + e.getMessage());
        }
    }
}
