package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/measuringroom/export")
@Controller
public class ExportController {

    @Autowired
    private TestRecordService testRecordService;

    @GetMapping("/test_score")
    public String exportTestScore(){
        return "measuringroom/export/testScore";
    }

    /**
     * 临时调试接口：检查HTML内容处理
     */
    @GetMapping("/debug-html")
    @ResponseBody
    public ResponseEntity<String> debugHtml(@RequestParam(required = false) String testHtml) {
        try {
            if (testHtml == null) {
                testHtml = "这是一个<span style='color: red;'>测试</span>内容，包含<strong>加粗</strong>和<em>斜体</em>。";
            }
            String result = testRecordService.debugHtmlProcessing(testHtml);
            return ResponseEntity.ok()
                    .header("Content-Type", "text/plain; charset=utf-8")
                    .body("原始内容: " + testHtml + "\n\n处理后: " + result + "\n\n请查看日志获取详细信息");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("调试失败：" + e.getMessage());
        }
    }
}
