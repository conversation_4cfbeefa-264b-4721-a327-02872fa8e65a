package cn.psycloud.psyplatform.util;

import cn.psycloud.psyplatform.dto.measuringroom.FactorExplainHierarchyDto;
import com.deepoove.poi.policy.AbstractRenderPolicy;
import com.deepoove.poi.template.ElementTemplate;
import com.deepoove.poi.template.run.RunTemplate;
import com.deepoove.poi.util.TableTools;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;
import java.util.List;

/**
 * 因子表格渲染策略 - 支持固定列宽
 */
public class FactorTableRenderPolicy extends AbstractRenderPolicy<List<FactorExplainHierarchyDto>> {
    
    private final List<FactorExplainHierarchyDto> allFactors;
    
    public FactorTableRenderPolicy(List<FactorExplainHierarchyDto> allFactors) {
        this.allFactors = allFactors;
    }

    @Override
    public void doRender(RunTemplate runTemplate, List<FactorExplainHierarchyDto> factors, XWPFTemplate template) throws Exception {
        XWPFRun run = runTemplate.getRun();
        if (null == factors || factors.isEmpty()) {
            run.setText("暂无数据", 0);
            return;
        }

        // 获取当前段落
        XWPFParagraph paragraph = (XWPFParagraph) run.getParent();
        XWPFDocument document = paragraph.getDocument();

        // 清空当前run的内容
        run.setText("", 0);

        // 在当前段落位置创建表格
        XWPFTable table = document.createTable();

        // 设置表格样式
        setupTableStyle(table);

        // 创建表头
        XWPFTableRow headerRow = table.getRow(0);
        setupHeaderRow(headerRow);

        // 添加数据行
        for (FactorExplainHierarchyDto factor : factors) {
            XWPFTableRow dataRow = table.createRow();
            setupDataRow(dataRow, factor);
        }

        // 设置列宽
        setColumnWidths(table);
    }
    
    /**
     * 设置表格样式
     */
    private void setupTableStyle(XWPFTable table) {
        // 设置表格边框
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }
        
        // 设置表格边框
        CTTblBorders borders = tblPr.addNewTblBorders();
        
        // 上边框
        CTBorder topBorder = borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4));
        topBorder.setColor("E8ECF4");
        
        // 下边框
        CTBorder bottomBorder = borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));
        bottomBorder.setColor("E8ECF4");
        
        // 左边框
        CTBorder leftBorder = borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));
        leftBorder.setColor("E8ECF4");
        
        // 右边框
        CTBorder rightBorder = borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));
        rightBorder.setColor("E8ECF4");
        
        // 内部水平边框
        CTBorder insideHBorder = borders.addNewInsideH();
        insideHBorder.setVal(STBorder.SINGLE);
        insideHBorder.setSz(BigInteger.valueOf(4));
        insideHBorder.setColor("E8ECF4");
        
        // 内部垂直边框
        CTBorder insideVBorder = borders.addNewInsideV();
        insideVBorder.setVal(STBorder.SINGLE);
        insideVBorder.setSz(BigInteger.valueOf(4));
        insideVBorder.setColor("E8ECF4");
        
        // 设置表格宽度为100%
        CTTblWidth tblWidth = tblPr.addNewTblW();
        tblWidth.setType(STTblWidth.PCT);
        tblWidth.setW(BigInteger.valueOf(5000)); // 100% = 5000
    }
    
    /**
     * 设置表头行
     */
    private void setupHeaderRow(XWPFTableRow headerRow) {
        // 确保有两列
        while (headerRow.getTableCells().size() < 2) {
            headerRow.createCell();
        }

        // 第一列：因子名称
        XWPFTableCell cell1 = headerRow.getCell(0);
        clearCellContent(cell1);
        cell1.setText("因子名称");
        setupHeaderCell(cell1);

        // 第二列：结果解释
        XWPFTableCell cell2 = headerRow.getCell(1);
        clearCellContent(cell2);
        cell2.setText("结果解释");
        setupHeaderCell(cell2);
    }
    
    /**
     * 清空单元格内容
     */
    private void clearCellContent(XWPFTableCell cell) {
        // 清空所有段落
        for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
            if (i > 0) { // 保留第一个段落
                cell.removeParagraph(i);
            }
        }
        // 清空第一个段落的内容
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }
    }

    /**
     * 设置表头单元格样式
     */
    private void setupHeaderCell(XWPFTableCell cell) {
        // 设置背景色
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }

        CTShd shd = tcPr.addNewShd();
        shd.setVal(STShd.CLEAR);
        shd.setColor("auto");
        shd.setFill("F8F9FF");

        // 设置字体样式
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.createRun();
        run.setBold(true);
        run.setColor("2C3E50");
        run.setFontFamily("宋体");
        run.setFontSize(12);

        // 设置段落对齐
        paragraph.setAlignment(ParagraphAlignment.LEFT);

        // 设置单元格内边距
        CTTblCellMar cellMar = tcPr.addNewTcMar();
        CTTblWidth leftMar = cellMar.addNewLeft();
        leftMar.setType(STTblWidth.DXA);
        leftMar.setW(BigInteger.valueOf(170)); // 12px ≈ 170 twips

        CTTblWidth rightMar = cellMar.addNewRight();
        rightMar.setType(STTblWidth.DXA);
        rightMar.setW(BigInteger.valueOf(170));

        CTTblWidth topMar = cellMar.addNewTop();
        topMar.setType(STTblWidth.DXA);
        topMar.setW(BigInteger.valueOf(170));

        CTTblWidth bottomMar = cellMar.addNewBottom();
        bottomMar.setType(STTblWidth.DXA);
        bottomMar.setW(BigInteger.valueOf(170));
    }
    
    /**
     * 设置数据行
     */
    private void setupDataRow(XWPFTableRow dataRow, FactorExplainHierarchyDto factor) {
        // 确保有两列
        while (dataRow.getTableCells().size() < 2) {
            dataRow.createCell();
        }

        // 第一列：因子名称
        XWPFTableCell cell1 = dataRow.getCell(0);
        clearCellContent(cell1);
        cell1.setText(factor.getFactorName() != null ? factor.getFactorName() : "");
        setupDataCell(cell1);

        // 第二列：结果解释
        XWPFTableCell cell2 = dataRow.getCell(1);
        clearCellContent(cell2);
        String interpretation = factor.getInterpretation() != null ? factor.getInterpretation() : "暂无解释内容";
        // 简单处理HTML标签
        interpretation = interpretation.replaceAll("<[^>]+>", "").trim();
        cell2.setText(interpretation);
        setupDataCell(cell2);
    }
    
    /**
     * 设置数据单元格样式
     */
    private void setupDataCell(XWPFTableCell cell) {
        // 设置字体样式
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.getRuns().isEmpty() ? paragraph.createRun() : paragraph.getRuns().get(0);
        run.setFontFamily("宋体");
        run.setFontSize(11);
        run.setColor("2C3E50");
        
        // 设置段落对齐和行距
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingLineRule(LineSpacingRule.MULTIPLE);
        paragraph.setSpacingBetween(1.6);
        
        // 设置单元格内边距
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }
        
        CTTblCellMar cellMar = tcPr.addNewTcMar();
        CTTblWidth leftMar = cellMar.addNewLeft();
        leftMar.setType(STTblWidth.DXA);
        leftMar.setW(BigInteger.valueOf(170)); // 12px ≈ 170 twips
        
        CTTblWidth rightMar = cellMar.addNewRight();
        rightMar.setType(STTblWidth.DXA);
        rightMar.setW(BigInteger.valueOf(170));
        
        CTTblWidth topMar = cellMar.addNewTop();
        topMar.setType(STTblWidth.DXA);
        topMar.setW(BigInteger.valueOf(170));
        
        CTTblWidth bottomMar = cellMar.addNewBottom();
        bottomMar.setType(STTblWidth.DXA);
        bottomMar.setW(BigInteger.valueOf(170));
        
        // 设置垂直对齐
        CTVerticalJc vAlign = tcPr.addNewVAlign();
        vAlign.setVal(STVerticalJc.CENTER);
    }
    
    /**
     * 设置列宽 - 第一列固定100px，第二列自适应
     */
    private void setColumnWidths(XWPFTable table) {
        // 获取表格网格
        CTTblGrid tblGrid = table.getCTTbl().getTblGrid();
        if (tblGrid == null) {
            tblGrid = table.getCTTbl().addNewTblGrid();
        }
        
        // 清除现有列定义
        tblGrid.getGridColList().clear();
        
        // 第一列：100px ≈ 1440 twips (1px = 14.4 twips)
        CTTblGridCol col1 = tblGrid.addNewGridCol();
        col1.setW(BigInteger.valueOf(1440));
        
        // 第二列：剩余宽度
        CTTblGridCol col2 = tblGrid.addNewGridCol();
        col2.setW(BigInteger.valueOf(8560)); // 总宽度10000 - 第一列1440 = 8560
        
        // 为每一行设置列宽
        for (XWPFTableRow row : table.getRows()) {
            List<XWPFTableCell> cells = row.getTableCells();
            if (cells.size() >= 2) {
                // 第一列宽度
                setCellWidth(cells.get(0), 1440);
                // 第二列宽度
                setCellWidth(cells.get(1), 8560);
            }
        }
    }
    
    /**
     * 设置单元格宽度
     */
    private void setCellWidth(XWPFTableCell cell, int width) {
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }
        
        CTTblWidth cellWidth = tcPr.addNewTcW();
        cellWidth.setType(STTblWidth.DXA);
        cellWidth.setW(BigInteger.valueOf(width));
    }
}
