package cn.psycloud.psyplatform.util;

import cn.psycloud.psyplatform.dto.measuringroom.FactorExplainHierarchyDto;

import java.util.List;

/**
 * Word表格列宽设置解决方案集合
 * 
 * 问题分析：
 * poi-tl-ext的HtmlRenderPolicy对CSS样式支持有限，特别是表格列宽设置
 * 
 * 解决方案：
 * 1. 方案一：使用原生POI API创建表格（最可靠，但复杂）
 * 2. 方案二：使用table-layout:fixed + 像素宽度（中等兼容性）
 * 3. 方案三：使用百分比宽度（最简单，兼容性最好）
 */
public class TableColumnWidthSolutions {
    
    /**
     * 方案一：原生POI API表格（推荐用于复杂表格）
     * 优点：完全控制表格样式，列宽精确
     * 缺点：代码复杂，需要自定义RenderPolicy
     */
    public static String generateNativePOITable(List<FactorExplainHierarchyDto> factors, String chartPrefix) {
        // 使用特殊标记，由FactorTableRenderPolicy处理
        return "{{factorTable_" + chartPrefix.replaceAll("[^a-zA-Z0-9]", "_") + "}}";
    }
    
    /**
     * 方案二：HTML表格 + table-layout:fixed（适用于固定像素宽度）
     * 优点：相对简单，支持固定像素宽度
     * 缺点：poi-tl-ext对table-layout支持有限
     */
    public static String generateFixedLayoutTable(List<FactorExplainHierarchyDto> factors) {
        StringBuilder tableHtml = new StringBuilder();
        
        tableHtml.append("<table style='width: 100%; table-layout: fixed; border-collapse: collapse; border: 1px solid #e8ecf4;'>");
        tableHtml.append("<col width='100'>");
        tableHtml.append("<col>");
        
        // 表头
        tableHtml.append("<tr style='background: #f8f9ff;'>");
        tableHtml.append("<th style='width: 100px; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>因子名称</th>");
        tableHtml.append("<th style='padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>结果解释</th>");
        tableHtml.append("</tr>");
        
        // 表体
        for (FactorExplainHierarchyDto factor : factors) {
            tableHtml.append("<tr>");
            tableHtml.append("<td style='width: 100px; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                    .append(escapeHtml(factor.getFactorName())).append("</td>");
            tableHtml.append("<td style='padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                    .append(cleanHtml(factor.getInterpretation())).append("</td>");
            tableHtml.append("</tr>");
        }
        tableHtml.append("</table>");
        
        return tableHtml.toString();
    }
    
    /**
     * 方案三：HTML表格 + 百分比宽度（推荐用于简单表格）
     * 优点：最简单，兼容性最好
     * 缺点：无法精确控制像素宽度
     */
    public static String generatePercentageTable(List<FactorExplainHierarchyDto> factors) {
        StringBuilder tableHtml = new StringBuilder();
        
        tableHtml.append("<table style='width: 100%; border-collapse: collapse; border: 1px solid #e8ecf4;'>");
        
        // 表头
        tableHtml.append("<tr style='background: #f8f9ff;'>");
        tableHtml.append("<th style='width: 20%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>因子名称</th>");
        tableHtml.append("<th style='width: 80%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>结果解释</th>");
        tableHtml.append("</tr>");
        
        // 表体
        for (FactorExplainHierarchyDto factor : factors) {
            tableHtml.append("<tr>");
            tableHtml.append("<td style='width: 20%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                    .append(escapeHtml(factor.getFactorName())).append("</td>");
            tableHtml.append("<td style='width: 80%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                    .append(cleanHtml(factor.getInterpretation())).append("</td>");
            tableHtml.append("</tr>");
        }
        tableHtml.append("</table>");
        
        return tableHtml.toString();
    }
    
    /**
     * 方案四：使用Word表格标记（最兼容的方案）
     * 优点：完全兼容Word，支持复杂样式
     * 缺点：需要修改模板文件
     */
    public static String generateWordTableMarkup(List<FactorExplainHierarchyDto> factors) {
        // 这种方案需要在Word模板中预先创建表格，然后使用poi-tl的表格渲染功能
        // 返回特殊标记，由模板处理
        return "{{#factorTable}}{{factorName}} | {{interpretation}}{{/factorTable}}";
    }
    
    /**
     * 方案五：混合方案 - 基本信息用固定表格，因子用百分比表格
     */
    public static String generateHybridTable(List<FactorExplainHierarchyDto> factors, boolean isBasicInfo) {
        if (isBasicInfo) {
            // 基本信息表格使用固定布局
            return generateFixedLayoutTable(factors);
        } else {
            // 因子表格使用百分比布局
            return generatePercentageTable(factors);
        }
    }
    
    /**
     * HTML转义
     */
    private static String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }
    
    /**
     * 清理HTML标签
     */
    private static String cleanHtml(String text) {
        if (text == null) return "暂无解释内容";
        return text.replaceAll("<[^>]+>", "").trim();
    }
    
    /**
     * 获取推荐方案
     * 根据不同场景返回最适合的方案
     */
    public static String getRecommendedSolution(List<FactorExplainHierarchyDto> factors, String tableType) {
        switch (tableType.toLowerCase()) {
            case "basic":
                // 基本信息表格，推荐使用百分比方案
                return generatePercentageTable(factors);
            case "factor":
                // 因子表格，推荐使用百分比方案
                return generatePercentageTable(factors);
            case "complex":
                // 复杂表格，推荐使用原生POI方案
                return generateNativePOITable(factors, "complex");
            default:
                // 默认使用百分比方案
                return generatePercentageTable(factors);
        }
    }
}
