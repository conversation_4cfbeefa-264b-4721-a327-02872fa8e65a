# Word表格列宽设置配置文件
# 
# 可选方案：
# percentage - 使用百分比宽度（推荐，兼容性最好）
# fixed - 使用固定像素宽度（中等兼容性）
# native - 使用原生POI API（最可靠，但复杂）

# 因子表格生成方案
factor.table.method=percentage

# 基本信息表格生成方案
basic.table.method=percentage

# 表格列宽设置
# 百分比方案的列宽比例
percentage.first.column.width=20
percentage.second.column.width=80

# 固定像素方案的列宽（单位：px）
fixed.first.column.width=100
fixed.second.column.auto=true

# 表格样式配置
table.border.color=#e8ecf4
table.header.background=#f8f9ff
table.cell.padding=12px
table.font.family=宋体
table.font.size=11
table.header.font.size=12
table.header.font.weight=bold
table.text.color=#2c3e50

# 调试模式（开启后会在日志中输出表格HTML）
debug.table.html=false
