# 删除线问题调试说明

## 问题描述

在Word文档生成过程中，结果解释内容出现了意外的删除线效果。

## 可能的原因分析

### 1. 正则表达式匹配问题
原始的删除线处理正则表达式可能过于宽泛：
```java
.replaceAll("<s\\s*[^>]*>", "<s>")  // 可能误匹配<span>等标签
```

### 2. HTML标签处理顺序问题
不同HTML标签的处理顺序可能导致意外的转换。

### 3. span标签处理问题
span标签的处理可能影响其他标签的正确解析。

## 已实施的修复措施

### 1. 改进正则表达式精确度
```java
// 修复前
.replaceAll("<s\\s*[^>]*>", "<s>")

// 修复后
.replaceAll("<s(\\s+[^>]*)?\\s*>", "<s>")  // 更精确的匹配
```

### 2. 优化span标签处理
```java
// 修复前
.replaceAll("<span\\s*>", "")
.replaceAll("</span>", "")

// 修复后
.replaceAll("<span\\s*>([^<]*?)</span>", "$1")  // 移除没有属性的span标签对
.replaceAll("<span\\s*></span>", "")  // 移除空的span标签
```

### 3. 添加调试日志
```java
// 调试：记录原始内容
if (text.contains("<s") || text.contains("<del") || text.contains("<strike") || text.contains("<span")) {
    log.warn("HTML处理调试 - 原始内容: {}", text);
}

// 调试：记录处理后的内容
if (result.contains("<s>") || result.contains("</s>")) {
    log.warn("HTML处理调试 - 处理后包含删除线标签，结果: {}", result);
}
```

## 调试方法

### 1. 使用调试接口
访问以下URL进行测试：
```
GET /measuringroom/export/debug-html?testHtml=<测试HTML内容>
```

例如：
```
/measuringroom/export/debug-html?testHtml=这是<span>测试</span>内容
```

### 2. 查看日志
在应用日志中查找以下关键词：
- "HTML处理调试"
- "发现删除线相关标签"
- "处理后包含删除线标签"

### 3. 测试用例
可以使用以下测试用例验证修复效果：

#### 正常情况
```html
这是<strong>加粗</strong>和<em>斜体</em>内容
```

#### 包含span的情况
```html
这是<span style="color: red;">红色</span>文字
```

#### 包含删除线的情况
```html
这是<s>删除线</s>内容
```

#### 复杂情况
```html
<p>段落内容包含<span>普通span</span>和<span style="color: blue;">样式span</span></p>
```

## 验证步骤

1. **生成Word文档**：使用实际数据生成Word文档
2. **检查删除线**：查看Word文档中是否还有意外的删除线
3. **查看日志**：检查调试日志中的HTML处理信息
4. **使用调试接口**：测试特定的HTML内容处理

## 后续优化建议

1. **完善正则表达式**：继续优化HTML标签匹配的精确度
2. **添加单元测试**：为HTML处理方法添加单元测试
3. **考虑使用HTML解析库**：如果问题复杂，考虑使用专门的HTML解析库

## 临时解决方案

如果问题仍然存在，可以考虑以下临时解决方案：

1. **完全移除删除线处理**：暂时注释掉删除线相关的处理代码
2. **使用白名单方式**：只保留确实需要的HTML标签
3. **分步处理**：将HTML处理分解为多个步骤，便于调试

## 修改的文件

- `TestRecordServiceImpl.java` - 改进HTML处理逻辑和添加调试
- `ExportController.java` - 添加调试接口

## 注意事项

- 调试代码仅用于问题排查，解决问题后应移除
- 日志级别设置为WARN以确保能够看到调试信息
- 测试时注意URL编码问题
