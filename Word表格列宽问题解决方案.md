# Word表格列宽问题解决方案

## 问题描述

在使用poi-tl-ext生成Word文档时，HTML表格的第一列宽度设置为100px没有生效，所有尝试的方法都无法实现预期的列宽控制。

## 问题原因分析

1. **CSS样式支持有限**：poi-tl-ext的HtmlRenderPolicy对CSS样式的支持不完整，特别是表格的`width`、`col`标签的`style`属性等
2. **HTML属性支持有限**：`width`属性在转换为Word时可能被忽略
3. **colgroup支持问题**：`<colgroup>`和`<col>`标签的样式可能不被正确解析

## 解决方案

### 方案一：使用原生POI API创建表格（最可靠）

**优点：**
- 完全控制表格样式
- 列宽精确到像素
- 支持复杂的表格格式

**缺点：**
- 代码复杂
- 需要自定义RenderPolicy

**实现：**
```java
// 使用FactorTableRenderPolicy类
// 文件位置：src/main/java/cn/psycloud/psyplatform/util/FactorTableRenderPolicy.java
```

### 方案二：使用table-layout:fixed + 像素宽度

**优点：**
- 相对简单
- 支持固定像素宽度

**缺点：**
- poi-tl-ext对table-layout支持有限

**实现：**
```html
<table style='width: 100%; table-layout: fixed; border-collapse: collapse;'>
    <colgroup>
        <col style='width: 100px;'>
        <col>
    </colgroup>
    <!-- 表格内容 -->
</table>
```

### 方案三：使用百分比宽度（推荐）

**优点：**
- 最简单
- 兼容性最好
- 适应性强

**缺点：**
- 无法精确控制像素宽度

**实现：**
```html
<table style='width: 100%; border-collapse: collapse;'>
    <tr>
        <th style='width: 20%;'>因子名称</th>
        <th style='width: 80%;'>结果解释</th>
    </tr>
    <!-- 表格内容 -->
</table>
```

## 当前实现

项目中已经实现了多种方案的支持，可以通过配置切换：

### 配置文件
```properties
# src/main/resources/table-config.properties
factor.table.method=percentage  # 可选：percentage, fixed, native
```

### 代码位置
- 主要实现：`TestRecordServiceImpl.java` 的 `generateFactorTableHtml()` 方法
- 工具类：`TableColumnWidthSolutions.java`
- 原生POI方案：`FactorTableRenderPolicy.java`

### 测试方法
访问以下URL进行测试：
```
GET /measuringroom/export/test-table-width?recordId=123
```

## 推荐使用方案

### 对于一般需求（推荐）
使用**方案三：百分比宽度**
- 设置第一列宽度为20%（约等于100px在标准页面宽度下）
- 第二列宽度为80%
- 兼容性最好，维护简单

### 对于精确控制需求
使用**方案一：原生POI API**
- 可以精确控制到像素级别
- 支持复杂的表格样式
- 代码复杂度较高

## 实施步骤

1. **立即可用方案**：
   - 当前代码已默认使用百分比方案
   - 第一列宽度设置为20%
   - 无需额外配置

2. **如需精确控制**：
   - 修改 `generateFactorTableHtml()` 方法中的 `tableMethod` 变量为 `"native"`
   - 确保 `FactorTableRenderPolicy` 正确配置

3. **测试验证**：
   - 使用测试接口验证效果
   - 检查生成的Word文件

## 注意事项

1. **版本兼容性**：当前使用poi-tl-ext 0.3.3版本，不同版本可能有不同的支持程度
2. **性能考虑**：原生POI方案性能略低于HTML方案
3. **维护性**：百分比方案维护成本最低

## 后续优化建议

1. 考虑升级poi-tl-ext到最新版本
2. 可以实现配置化的列宽设置
3. 添加更多的表格样式选项

## 总结

问题的根本原因是poi-tl-ext对CSS样式支持有限。通过实施多种解决方案，特别是百分比宽度方案，可以有效解决表格列宽控制问题。推荐使用百分比方案作为默认解决方案，在需要精确控制时使用原生POI API方案。
