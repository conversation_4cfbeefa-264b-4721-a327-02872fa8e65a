# Word表格列宽问题解决方案

## 问题描述

在使用poi-tl-ext生成Word文档时，HTML表格的第一列宽度设置为100px没有生效。

## 问题原因

poi-tl-ext的HtmlRenderPolicy对CSS样式支持有限，特别是表格的固定像素宽度设置。

## 解决方案：使用百分比宽度

采用百分比宽度设置表格列宽，第一列20%，第二列80%。

### 优点
- 兼容性最好
- 代码简单
- 维护成本低
- 在标准页面宽度下，20%约等于100px的效果

### 实现代码

```java
/**
 * 生成因子表格HTML - 使用百分比宽度（第一列20%，第二列80%）
 */
private String generateFactorTableHtml(List<FactorExplainHierarchyDto> factors) {
    StringBuilder tableHtml = new StringBuilder();
    
    tableHtml.append("<table style='width: 100%; border-collapse: collapse; border: 1px solid #e8ecf4;'>");
    
    // 表头
    tableHtml.append("<tr style='background: #f8f9ff;'>");
    tableHtml.append("<th style='width: 20%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>因子名称</th>");
    tableHtml.append("<th style='width: 80%; padding: 12px; text-align: left; border: 1px solid #e8ecf4; font-weight: bold; color: #2c3e50;'>结果解释</th>");
    tableHtml.append("</tr>");
    
    // 表体
    for (FactorExplainHierarchyDto factor : factors) {
        tableHtml.append("<tr>");
        tableHtml.append("<td style='width: 20%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle;'>")
                .append(escapeHtml(factor.getFactorName())).append("</td>");
        tableHtml.append("<td style='width: 80%; padding: 12px; border: 1px solid #e8ecf4; vertical-align: middle; line-height: 1.6;'>")
                .append(convertHtmlToDisplayStyle(factor.getInterpretation() != null ? factor.getInterpretation() : "暂无解释内容")).append("</td>");
        tableHtml.append("</tr>");
    }
    tableHtml.append("</table>");
    
    return tableHtml.toString();
}
```

## 修改的文件

1. **TestRecordServiceImpl.java**
   - 修改了 `generateFactorContent()` 方法
   - 简化了 `generateFactorTableHtml()` 方法
   - 使用百分比宽度设置表格列宽

## 使用说明

1. **无需配置**：代码已经默认使用百分比方案
2. **自定义列宽**：如需调整比例，修改CSS中的width值
3. **样式调整**：可以通过修改CSS样式调整表格外观

## 效果

- 第一列（因子名称）：占表格宽度的20%
- 第二列（结果解释）：占表格宽度的80%
- 完全兼容poi-tl-ext和Word文档格式
- 表格样式美观，边框和背景色保持一致

## 总结

通过使用百分比宽度方案，成功解决了Word表格列宽控制问题。该方案简单可靠，兼容性好，能够满足实际使用需求。
