# HTML样式在Word中显示的解决方案

## 问题描述

在生成Word文档时，结果解释内容中的HTML标签样式没有在Word中正确显示，所有HTML标签都被移除了。

## 解决方案

### 1. 改进HTML处理方法

修改了`convertHtmlToDisplayStyle`方法，不再简单地移除HTML标签，而是保留并规范化poi-tl-ext支持的HTML标签。

#### 支持的HTML标签和样式：

- **段落标签**：`<p>` - 保留段落结构
- **换行标签**：`<br/>` - 规范化为标准格式
- **加粗标签**：`<strong>`, `<b>` - 统一转换为`<strong>`
- **斜体标签**：`<em>`, `<i>` - 统一转换为`<em>`
- **下划线标签**：`<u>` - 保留下划线样式
- **删除线标签**：`<s>`, `<strike>`, `<del>` - 统一转换为`<s>`
- **标题标签**：`<h1>` 到 `<h6>` - 保留标题层级
- **列表标签**：`<ul>`, `<ol>`, `<li>` - 保留列表结构
- **字体标签**：`<font>` - 转换为`<span style='...'>`格式

### 2. 增强HTML渲染配置

改进了poi-tl-ext的HTML渲染配置，尝试设置更多的渲染选项：

```java
// 创建HTML渲染策略，配置支持更多HTML样式
org.ddr.poi.html.HtmlRenderPolicy htmlRenderPolicy = new org.ddr.poi.html.HtmlRenderPolicy();

// 配置HTML渲染选项（如果poi-tl-ext版本支持）
try {
    // 尝试设置HTML渲染配置，增强样式支持
    Class<?> configClass = Class.forName("org.ddr.poi.html.HtmlRenderConfig");
    Object config = configClass.newInstance();
    
    // 设置全局字体
    configClass.getMethod("setGlobalFont", String.class).invoke(config, "宋体");
    
    // 设置全局字号
    configClass.getMethod("setGlobalFontSize", Integer.class).invoke(config, 11);
    
    // 应用配置到渲染策略
    htmlRenderPolicy.getClass().getMethod("setConfig", configClass).invoke(htmlRenderPolicy, config);
    
} catch (Exception e) {
    // 如果配置失败，使用默认配置
    log.debug("HTML渲染配置设置失败，使用默认配置: {}", e.getMessage());
}
```

### 3. HTML标签规范化处理

#### 处理逻辑：

1. **规范化标签属性**：移除不必要的属性，保留标签本身
2. **统一标签格式**：将不同的同类标签统一为标准格式
3. **清理无效标签**：移除不支持的标签，转换为支持的格式
4. **保留样式信息**：对于有样式的标签，尽可能保留样式信息

#### 具体转换规则：

```java
// 规范化段落标签
.replaceAll("<p\\s+[^>]*>", "<p>")  // 移除p标签的属性，保留标签本身

// 规范化换行标签
.replaceAll("<br[^>]*/?\\s*>", "<br/>")

// 规范化加粗标签 - 统一使用strong
.replaceAll("<b\\s*[^>]*>", "<strong>")
.replaceAll("</b>", "</strong>")

// 规范化斜体标签 - 统一使用em
.replaceAll("<i\\s*[^>]*>", "<em>")
.replaceAll("</i>", "</em>")

// 处理font标签 - 转换为span with style
.replaceAll("<font\\s+color\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>", "<span style='color: $1;'>")
.replaceAll("</font>", "</span>")
```

## 实现的改进

### 1. 修改的文件

- **TestRecordServiceImpl.java**：
  - 改进了`convertHtmlToDisplayStyle`方法
  - 增强了HTML渲染配置
  - 添加了测试方法`testHtmlStylesInWord`

- **ExportController.java**：
  - 添加了测试接口`/measuringroom/export/test-html-styles`

### 2. 核心改进代码

```java
/**
 * 处理HTML内容以便在Word中正确显示样式
 * @param text 包含HTML标签的文本
 * @return 处理后的HTML文本，保留poi-tl-ext支持的标签和样式
 */
private String convertHtmlToDisplayStyle(String text) {
    if (text == null) {
        return "";
    }

    // 保留HTML标签，让poi-tl-ext进行渲染
    // poi-tl-ext支持的HTML标签：p, br, strong, b, em, i, u, s, ul, ol, li, h1-h6等
    String result = text
            // 规范化段落标签
            .replaceAll("<p\\s+[^>]*>", "<p>")
            
            // 规范化换行标签
            .replaceAll("<br[^>]*/?\\s*>", "<br/>")
            
            // 规范化加粗标签 - 统一使用strong
            .replaceAll("<b\\s*[^>]*>", "<strong>")
            .replaceAll("</b>", "</strong>")
            
            // 规范化斜体标签 - 统一使用em
            .replaceAll("<i\\s*[^>]*>", "<em>")
            .replaceAll("</i>", "</em>")
            
            // 保留下划线标签
            .replaceAll("<u\\s*[^>]*>", "<u>")
            
            // 保留删除线标签
            .replaceAll("<s\\s*[^>]*>", "<s>")
            .replaceAll("<strike\\s*[^>]*>", "<s>")
            .replaceAll("</strike>", "</s>")
            .replaceAll("<del\\s*[^>]*>", "<s>")
            .replaceAll("</del>", "</s>")
            
            // 规范化标题标签
            .replaceAll("<h([1-6])\\s*[^>]*>", "<h$1>")
            
            // 规范化列表标签
            .replaceAll("<ul\\s*[^>]*>", "<ul>")
            .replaceAll("<ol\\s*[^>]*>", "<ol>")
            .replaceAll("<li\\s*[^>]*>", "<li>")
            
            // 处理font标签 - 转换为span with style
            .replaceAll("<font\\s+color\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>", "<span style='color: $1;'>")
            .replaceAll("</font>", "</span>")
            
            // 清理多余的空白段落和换行
            .replaceAll("(<p>\\s*</p>)", "")
            .replaceAll("(<br/>\\s*){3,}", "<br/><br/>")
            .replaceAll("^(<br/>\\s*)+", "")
            .replaceAll("(<br/>\\s*)+$", "");

    return result;
}
```

## 测试方法

### 1. 测试接口

访问以下URL进行测试：
```
GET /measuringroom/export/test-html-styles?recordId=123
```

### 2. 测试功能

- 检查因子解释中的HTML内容
- 显示原始HTML和处理后的HTML
- 统计包含HTML的因子数量
- 生成测试Word文档
- 列出支持的HTML标签

### 3. 测试结果

测试接口会返回详细的测试信息，包括：
- 总因子数和包含HTML的因子数
- 每个包含HTML的因子的处理前后对比
- Word文档生成结果
- 支持的HTML标签列表

## 使用说明

1. **无需额外配置**：改进后的代码会自动处理HTML标签
2. **兼容性**：保持与现有代码的完全兼容
3. **测试验证**：使用测试接口验证HTML样式显示效果

## 注意事项

1. **poi-tl-ext版本**：当前使用0.3.3版本，不同版本对HTML标签的支持可能有差异
2. **样式限制**：复杂的CSS样式可能不被完全支持
3. **字体设置**：建议使用系统支持的字体

## 总结

通过改进HTML处理方法和增强渲染配置，现在可以在Word文档中正确显示HTML标签的样式效果，包括加粗、斜体、下划线、删除线、列表等常用格式。这大大提升了Word文档的可读性和专业性。
